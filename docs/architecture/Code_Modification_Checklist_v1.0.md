# 代码修改清单 - PSDK日志增强

## 1. 新增文件

### 1.1 PSDKLogger.java
**路径**: `app/src/main/java/com/skysys/fly/util/PSDKLogger.java`
**描述**: PSDK专用日志工具类
**功能**: 
- 统一的PSDK日志输出接口
- 基于XLog框架的封装
- 支持分级日志和性能优化

## 2. 修改文件

### 2.1 MissionController.java
**路径**: `app/src/main/java/com/skysys/fly/page/fly/controller/MissionController.java`

#### 2.1.1 导入语句添加 (第1-105行区域)
**位置**: 在现有import语句后添加
**内容**: 
```java
import com.skysys.fly.util.PSDKLogger;
```

#### 2.1.2 Payload获取日志 (第285-287行)
**原代码**:
```java
payload = DJISDKManager.getInstance().getProduct().getPayload();
if(payload == null){
    ToastUtil.show("没有获取到payload");
}
```

**修改后**:
```java
payload = DJISDKManager.getInstance().getProduct().getPayload();
if(payload == null){
    PSDKLogger.logConnection("GET_PAYLOAD", false, "Payload对象获取失败");
    ToastUtil.show("没有获取到payload");
} else {
    PSDKLogger.logConnection("GET_PAYLOAD", true, "Payload对象获取成功");
}
```

#### 2.1.3 回调设置日志 (第289行)
**原代码**:
```java
payload.setCommandDataCallback(new Payload.CommandDataCallback() {
```

**修改后**:
```java
try {
    payload.setCommandDataCallback(new Payload.CommandDataCallback() {
```

**在回调设置后添加** (第350行后):
```java
    });
    PSDKLogger.logConnection("SET_CALLBACK", true, "CommandDataCallback设置成功");
} catch (Exception e) {
    PSDKLogger.logError("SET_CALLBACK", e);
}
```

#### 2.1.4 数据接收日志 (第291行)
**原代码**:
```java
public void onGetCommandData(byte[] bytes) {
    if(bytes != null){
```

**修改后**:
```java
public void onGetCommandData(byte[] bytes) {
    PSDKLogger.logDataReceive(bytes, bytes != null ? bytes.length : 0);
    if(bytes != null){
```

#### 2.1.5 数据分类日志 (第293行)
**原代码**:
```java
if(bytes.length >= 32 && bytes.length < 48){
```

**修改后**:
```java
if(bytes.length >= 32 && bytes.length < 48){
    PSDKLogger.logDataParse("DATA_CATEGORY", bytes.length, true, "数据长度32-47，处理CO2和温度");
```

#### 2.1.6 CO2数据解析日志 (第298-299行)
**原代码**:
```java
payloadData.setCO2Value1(getFloat(bytesCo2));
payloadData.setCO2Value2(getFloat(bytesCo2));
```

**修改后**:
```java
try {
    float co2Value = getFloat(bytesCo2);
    payloadData.setCO2Value1(co2Value);
    payloadData.setCO2Value2(co2Value);
    PSDKLogger.logDataParse("CO2", co2Value, true, "CO2数据解析成功");
} catch (Exception e) {
    PSDKLogger.logError("PARSE_CO2", e);
}
```

#### 2.1.7 温度数据解析日志 (第300-301行)
**原代码**:
```java
payloadData.setTemperature1(getFloat(bytesTem3));
payloadData.setTemperature2(getFloat(bytesTem3));
```

**修改后**:
```java
try {
    float tempValue = getFloat(bytesTem3);
    payloadData.setTemperature1(tempValue);
    payloadData.setTemperature2(tempValue);
    PSDKLogger.logDataParse("TEMPERATURE", tempValue, true, "温度数据解析成功");
} catch (Exception e) {
    PSDKLogger.logError("PARSE_TEMPERATURE", e);
}
```

#### 2.1.8 数据存储日志 (第302行)
**原代码**:
```java
MApplication.getInstance().setPayloadData(payloadData);
```

**修改后**:
```java
try {
    MApplication.getInstance().setPayloadData(payloadData);
    PSDKLogger.logDataStore("UPDATE_PAYLOAD_DATA", true, "短数据包存储成功");
} catch (Exception e) {
    PSDKLogger.logError("STORE_PAYLOAD_DATA", e);
}
```

#### 2.1.9 长数据包分类日志 (第303行)
**原代码**:
```java
}else if(bytes.length >= 48){
```

**修改后**:
```java
}else if(bytes.length >= 48){
    PSDKLogger.logDataParse("DATA_CATEGORY", bytes.length, true, "数据长度>=48，处理湿度和压力");
```

#### 2.1.10 湿度数据解析日志 (第316-317行)
**原代码**:
```java
payloadData.setHumidity1(getFloat(bytesHumidity1));
payloadData.setHumidity2(getFloat(bytesHumidity2));
```

**修改后**:
```java
try {
    float humidity1 = getFloat(bytesHumidity1);
    float humidity2 = getFloat(bytesHumidity2);
    payloadData.setHumidity1(humidity1);
    payloadData.setHumidity2(humidity2);
    PSDKLogger.logDataParse("HUMIDITY", humidity1, true, "湿度1: " + humidity1 + ", 湿度2: " + humidity2);
} catch (Exception e) {
    PSDKLogger.logError("PARSE_HUMIDITY", e);
}
```

#### 2.1.11 压力数据解析日志 (第318-319行)
**原代码**:
```java
payloadData.setPressure1(getFloat(bytesPressure1));
payloadData.setPressure2(getFloat(bytesPressure2));
```

**修改后**:
```java
try {
    float pressure1 = getFloat(bytesPressure1);
    float pressure2 = getFloat(bytesPressure2);
    payloadData.setPressure1(pressure1);
    payloadData.setPressure2(pressure2);
    PSDKLogger.logDataParse("PRESSURE", pressure1, true, "压力1: " + pressure1 + ", 压力2: " + pressure2);
} catch (Exception e) {
    PSDKLogger.logError("PARSE_PRESSURE", e);
}
```

#### 2.1.12 长数据包存储日志 (第320行)
**原代码**:
```java
MApplication.getInstance().setPayloadData(payloadData);
```

**修改后**:
```java
try {
    MApplication.getInstance().setPayloadData(payloadData);
    PSDKLogger.logDataStore("UPDATE_PAYLOAD_DATA", true, "长数据包存储成功");
} catch (Exception e) {
    PSDKLogger.logError("STORE_PAYLOAD_DATA", e);
}
```

#### 2.1.13 回调清理日志 (第360-362行)
**原代码**:
```java
if(payload != null){
    payload.setCommandDataCallback(null);
}
```

**修改后**:
```java
if(payload != null){
    try {
        payload.setCommandDataCallback(null);
        PSDKLogger.logConnection("CLEAR_CALLBACK", true, "CommandDataCallback清理成功");
    } catch (Exception e) {
        PSDKLogger.logError("CLEAR_CALLBACK", e);
    }
}
```

#### 2.1.14 数据为空异常处理 (第322行后添加)
**在现有if(bytes != null)判断后添加else分支**:
```java
} else {
    PSDKLogger.logDataReceive(null, 0, "接收到空数据");
}
```

## 3. 风险评估

### 3.1 低风险修改
- **导入语句添加**: 无风险
- **日志语句添加**: 低风险，不影响原有逻辑
- **try-catch包装**: 低风险，增强异常处理

### 3.2 中等风险修改
- **变量提取**: 将getFloat()结果先赋值给变量，可能影响性能
- **异常处理逻辑**: 新增的异常捕获可能改变错误处理流程

### 3.3 风险缓解措施
- **渐进式修改**: 分批次进行修改和测试
- **功能开关**: 通过配置控制日志功能启用
- **性能监控**: 实时监控修改后的性能影响
- **回滚准备**: 保留原始代码备份

## 4. 测试要求

### 4.1 单元测试
- [ ] PSDKLogger各方法功能测试
- [ ] 异常情况处理测试
- [ ] 日志格式验证测试

### 4.2 集成测试
- [ ] PSDK设备连接测试
- [ ] 数据接收和解析测试
- [ ] 长时间运行稳定性测试

### 4.3 性能测试
- [ ] 日志输出性能影响测试
- [ ] 内存使用监控
- [ ] CPU占用率分析

### 4.4 回归测试
- [ ] 原有PSDK功能验证
- [ ] 数据显示正确性验证
- [ ] 异常情况处理验证

## 5. 部署检查清单

### 5.1 代码检查
- [ ] 所有修改点已实现
- [ ] 代码格式符合规范
- [ ] 异常处理完整
- [ ] 性能优化到位

### 5.2 功能检查
- [ ] 日志输出正常
- [ ] 原有功能不受影响
- [ ] 异常情况有对应日志
- [ ] 日志格式统一

### 5.3 性能检查
- [ ] 性能影响在预期范围内
- [ ] 内存使用正常
- [ ] 无内存泄漏
- [ ] 响应时间无明显增加

---

**清单状态**: ✅ 已完成
**预计修改行数**: ~50行新增，~20行修改
**预计工作量**: 60-90分钟
**下一步**: Alex开始实施代码修改

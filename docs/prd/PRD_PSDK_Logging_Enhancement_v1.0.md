# PRD: PSDK负载设备数据获取日志增强

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-29
- **负责人**: Emma (产品经理)
- **项目**: AircraftActivity PSDK日志增强

## 2. 背景与问题陈述

### 2.1 当前问题
当前AircraftActivity中的PSDK负载设备数据获取缺乏必要的日志记录，导致：
- 无法追踪负载设备连接状态
- 数据解析过程不透明，调试困难
- 异常情况无法及时发现和定位
- 数据传输质量无法评估

### 2.2 业务价值
通过添加完善的日志系统，可以：
- 提高系统可维护性和可调试性
- 快速定位PSDK通信问题
- 监控负载设备数据质量
- 为后续优化提供数据支撑

## 3. 目标与成功指标

### 3.1 项目目标
- **主要目标**: 为PSDK负载设备数据获取添加完整的日志记录
- **次要目标**: 建立标准化的日志格式和等级

### 3.2 关键结果(KRs)
- KR1: 100%覆盖PSDK数据获取关键节点的日志记录
- KR2: 实现分级日志输出(DEBUG/INFO/WARN/ERROR)
- KR3: 日志信息包含时间戳、数据长度、解析结果等关键信息

### 3.3 成功指标
- 所有PSDK数据回调都有对应日志
- 异常情况能通过日志快速定位
- 日志格式统一且易于阅读

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 开发工程师、测试工程师
- **次要用户**: 技术支持人员、运维人员

### 4.2 用户故事
- 作为开发工程师，我希望能通过日志快速了解PSDK数据获取状态
- 作为测试工程师，我希望能通过日志验证负载设备数据的正确性
- 作为技术支持，我希望能通过日志快速定位客户问题

## 5. 功能规格详述

### 5.1 日志添加位置

#### 5.1.1 Payload获取阶段
**位置**: MissionController.java 第285-287行
**日志内容**:
- Payload对象获取成功/失败状态
- 设备连接信息

#### 5.1.2 数据回调注册阶段  
**位置**: MissionController.java 第289行
**日志内容**:
- CommandDataCallback注册状态
- 回调函数设置结果

#### 5.1.3 数据接收阶段
**位置**: MissionController.java onGetCommandData方法内
**日志内容**:
- 接收到的原始数据长度
- 数据内容(十六进制格式)
- 数据接收时间戳

#### 5.1.4 数据解析阶段
**位置**: 数据长度判断和解析逻辑中
**日志内容**:
- 数据长度分类结果
- 各传感器数据解析结果
- 解析后的具体数值

#### 5.1.5 数据存储阶段
**位置**: MApplication.getInstance().setPayloadData调用处
**日志内容**:
- 数据存储操作结果
- 存储的完整数据对象

### 5.2 日志格式规范

#### 5.2.1 日志标签
- 统一使用 "PSDK_PAYLOAD" 作为主标签
- 子标签: CONNECTION, DATA_RECEIVE, DATA_PARSE, DATA_STORE

#### 5.2.2 日志等级
- **DEBUG**: 详细的数据内容和解析过程
- **INFO**: 关键操作状态和结果
- **WARN**: 异常但可恢复的情况
- **ERROR**: 严重错误和失败情况

#### 5.2.3 日志格式模板
```
[TIMESTAMP] [LEVEL] [TAG] [OPERATION] - [MESSAGE] | [DATA]
```

### 5.3 具体实现要求

#### 5.3.1 必须添加的日志点
1. Payload对象获取结果
2. CommandDataCallback设置结果  
3. 每次数据接收的原始信息
4. 数据长度分类和处理路径
5. CO2、温度、湿度、压力数据解析结果
6. PayloadData对象更新结果
7. 异常情况和错误处理

#### 5.3.2 日志内容要求
- 包含时间戳信息
- 数据长度和内容摘要
- 解析后的具体数值
- 操作成功/失败状态
- 异常信息详情

## 6. 范围定义

### 6.1 包含功能(In Scope)
- MissionController.java中PSDK相关日志添加
- 标准化日志格式定义
- 分级日志输出实现
- 异常情况日志记录

### 6.2 排除功能(Out of Scope)  
- 其他Activity的日志改造
- 日志文件持久化存储
- 日志远程上传功能
- 日志可视化界面

## 7. 依赖与风险

### 7.1 技术依赖
- Android Log系统
- 现有PSDK集成框架
- MissionController类结构

### 7.2 潜在风险
- **性能风险**: 频繁日志输出可能影响性能
- **存储风险**: 大量日志可能占用存储空间
- **安全风险**: 敏感数据可能泄露到日志中

### 7.3 风险缓解策略
- 使用条件编译控制日志输出
- 设置日志等级过滤
- 避免记录敏感信息

## 8. 发布初步计划

### 8.1 开发阶段
- 代码修改和日志添加
- 单元测试验证
- 代码审查

### 8.2 测试阶段  
- 功能测试验证日志输出
- 性能测试评估影响
- 集成测试确保兼容性

### 8.3 发布计划
- 内部测试版本发布
- 正式版本集成
- 文档更新和培训

## 9. 验收标准

### 9.1 功能验收
- [ ] 所有指定位置都添加了相应日志
- [ ] 日志格式符合规范要求
- [ ] 日志等级设置正确
- [ ] 异常情况有对应日志记录

### 9.2 质量验收
- [ ] 日志信息准确完整
- [ ] 性能影响在可接受范围内
- [ ] 代码审查通过
- [ ] 测试用例全部通过

---

**文档状态**: ✅ 已完成
**下一步**: 技术架构设计和开发实施

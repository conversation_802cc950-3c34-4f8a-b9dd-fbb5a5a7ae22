package com.skysys.fly.util;

import com.skysys.fly.BuildConfig;

/**
 * PSDK负载设备专用日志工具类
 * 基于XLog框架，提供统一的PSDK日志输出接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-29
 */
public class PSDKLogger {
    
    // 日志标签常量
    private static final String TAG_MAIN = "PSDK_PAYLOAD";
    private static final String TAG_CONNECTION = "PSDK_CONNECTION";
    private static final String TAG_DATA_RECEIVE = "PSDK_DATA_RECEIVE";
    private static final String TAG_DATA_PARSE = "PSDK_DATA_PARSE";
    private static final String TAG_DATA_STORE = "PSDK_DATA_STORE";
    private static final String TAG_ERROR = "PSDK_ERROR";
    
    // 日志等级控制
    private static final boolean FORCE_DEBUG = true; // 强制启用调试日志，可手动控制
    private static final boolean DEBUG_ENABLED = BuildConfig.DEBUG || FORCE_DEBUG;
    private static final boolean INFO_ENABLED = true;
    private static final boolean WARN_ENABLED = true;
    private static final boolean ERROR_ENABLED = true;

    // 数据日志配置
    private static final int MAX_DATA_LOG_LENGTH = 64; // 最大数据日志长度
    private static final boolean ENABLE_DATA_CONTENT_LOG = DEBUG_ENABLED;
    
    // XLog实例 - 复用现有框架
    private static MyLogger getLogger(String tag) {
        return new XLogUtil<>(tag).getLogger();
    }
    
    /**
     * 记录连接相关日志
     * @param operation 操作类型 (GET_PAYLOAD, SET_CALLBACK, CLEAR_CALLBACK等)
     * @param success 操作是否成功
     * @param details 详细信息
     */
    public static void logConnection(String operation, boolean success, String details) {
        if (!INFO_ENABLED) return;
        
        try {
            String status = success ? "SUCCESS" : "FAILED";
            String message = String.format("[%s] - %s | %s", operation, status, details);
            
            MyLogger logger = getLogger(TAG_CONNECTION);
            if (success) {
                logger.i(message);
            } else {
                logger.w(message);
            }
        } catch (Exception e) {
            // 避免日志错误影响业务逻辑
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 记录数据接收日志
     * @param data 接收到的字节数组
     * @param length 数据长度
     */
    public static void logDataReceive(byte[] data, int length) {
        logDataReceive(data, length, null);
    }
    
    /**
     * 记录数据接收日志
     * @param data 接收到的字节数组
     * @param length 数据长度
     * @param extraInfo 额外信息
     */
    public static void logDataReceive(byte[] data, int length, String extraInfo) {
        if (!DEBUG_ENABLED) return;
        
        try {
            StringBuilder message = new StringBuilder();
            message.append("[DATA_RECEIVE] - INFO | ");
            
            if (data == null) {
                message.append("接收到空数据");
                if (extraInfo != null) {
                    message.append(" | ").append(extraInfo);
                }
            } else {
                message.append("接收数据 | Length: ").append(length);
                
                // 添加数据内容摘要
                if (ENABLE_DATA_CONTENT_LOG && length > 0) {
                    message.append(", Data: ").append(bytesToHexString(data, Math.min(length, MAX_DATA_LOG_LENGTH)));
                    if (length > MAX_DATA_LOG_LENGTH) {
                        message.append("...");
                    }
                }
                
                if (extraInfo != null) {
                    message.append(" | ").append(extraInfo);
                }
            }
            
            getLogger(TAG_DATA_RECEIVE).d(message.toString(), false);
        } catch (Exception e) {
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 记录数据解析日志
     * @param dataType 数据类型 (CO2, TEMPERATURE, HUMIDITY, PRESSURE等)
     * @param value 解析后的数值
     * @param success 解析是否成功
     */
    public static void logDataParse(String dataType, float value, boolean success) {
        logDataParse(dataType, value, success, null);
    }
    
    /**
     * 记录数据解析日志
     * @param dataType 数据类型
     * @param value 解析后的数值
     * @param success 解析是否成功
     * @param details 详细信息
     */
    public static void logDataParse(String dataType, float value, boolean success, String details) {
        if (!DEBUG_ENABLED && success) return; // 成功的解析在非DEBUG模式下不记录
        
        try {
            String status = success ? "SUCCESS" : "FAILED";
            StringBuilder message = new StringBuilder();
            message.append("[").append(dataType).append("] - ").append(status);
            
            if (success) {
                message.append(" | Value: ").append(String.format("%.2f", value));
            }
            
            if (details != null) {
                message.append(" | ").append(details);
            }
            
            MyLogger logger = getLogger(TAG_DATA_PARSE);
            if (success) {
                logger.d(message.toString(), false);
            } else {
                logger.w(message.toString());
            }
        } catch (Exception e) {
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 记录数据存储日志
     * @param operation 存储操作类型
     * @param success 存储是否成功
     */
    public static void logDataStore(String operation, boolean success) {
        logDataStore(operation, success, null);
    }
    
    /**
     * 记录数据存储日志
     * @param operation 存储操作类型
     * @param success 存储是否成功
     * @param details 详细信息
     */
    public static void logDataStore(String operation, boolean success, String details) {
        if (!INFO_ENABLED) return;
        
        try {
            String status = success ? "SUCCESS" : "FAILED";
            StringBuilder message = new StringBuilder();
            message.append("[").append(operation).append("] - ").append(status);
            
            if (details != null) {
                message.append(" | ").append(details);
            }
            
            MyLogger logger = getLogger(TAG_DATA_STORE);
            if (success) {
                logger.i(message.toString());
            } else {
                logger.w(message.toString());
            }
        } catch (Exception e) {
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 记录错误日志
     * @param operation 操作类型
     * @param exception 异常对象
     */
    public static void logError(String operation, Exception exception) {
        if (!ERROR_ENABLED) return;
        
        try {
            StringBuilder message = new StringBuilder();
            message.append("[").append(operation).append("] - ERROR | ");
            message.append(exception.getClass().getSimpleName());
            
            if (exception.getMessage() != null) {
                message.append(": ").append(exception.getMessage());
            }
            
            getLogger(TAG_ERROR).e(message.toString());
            
            // DEBUG模式下打印堆栈跟踪
            if (DEBUG_ENABLED) {
                exception.printStackTrace();
            }
        } catch (Exception e) {
            // 最后的异常处理，避免日志系统本身出错
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 记录警告日志
     * @param operation 操作类型
     * @param message 警告信息
     */
    public static void logWarning(String operation, String message) {
        if (!WARN_ENABLED) return;
        
        try {
            String logMessage = String.format("[%s] - WARNING | %s", operation, message);
            getLogger(TAG_MAIN).w(logMessage);
        } catch (Exception e) {
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 记录信息日志
     * @param operation 操作类型
     * @param message 信息内容
     */
    public static void logInfo(String operation, String message) {
        if (!INFO_ENABLED) return;
        
        try {
            String logMessage = String.format("[%s] - INFO | %s", operation, message);
            getLogger(TAG_MAIN).i(logMessage);
        } catch (Exception e) {
            if (DEBUG_ENABLED) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @param maxLength 最大长度
     * @return 十六进制字符串
     */
    private static String bytesToHexString(byte[] bytes, int maxLength) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        int length = Math.min(bytes.length, maxLength);
        
        for (int i = 0; i < length; i++) {
            sb.append(String.format("%02X", bytes[i] & 0xFF));
            if (i < length - 1) {
                sb.append(" ");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 获取当前日志配置信息
     * @return 配置信息字符串
     */
    public static String getLogConfig() {
        return String.format("PSDKLogger Config - DEBUG: %s, INFO: %s, WARN: %s, ERROR: %s, DataContent: %s, MaxDataLength: %d",
                DEBUG_ENABLED, INFO_ENABLED, WARN_ENABLED, ERROR_ENABLED, ENABLE_DATA_CONTENT_LOG, MAX_DATA_LOG_LENGTH);
    }
}

package com.skysys.fly;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.os.Bundle;

import androidx.multidex.MultiDex;

import com.cySdkyc.clx.Helper;
import com.elvishew.xlog.BuildConfig;
import com.elvishew.xlog.LogConfiguration;
import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.XLog;
import com.elvishew.xlog.flattener.PatternFlattener;
import com.elvishew.xlog.printer.Printer;
import com.elvishew.xlog.printer.file.FilePrinter;
import com.otaliastudios.cameraview.PictureResult;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.mqtt.MQttManager;
import com.skysys.fly.data.mission.MissionStatus;
import com.skysys.fly.data.mission.PayloadData;
import com.skysys.fly.data.preference.SpUtil;
import com.skysys.fly.net.bean.UAVInfoSN;
import com.skysys.fly.util.XlogFileNameGenerator;
import com.tencent.bugly.crashreport.CrashReport;
import com.skysys.fly.common.drone.DJIHelper;

import dji.common.error.DJIError;
import dji.sdk.sdkmanager.DJISDKInitEvent;
import dji.sdk.sdkmanager.DJISDKManager;
import retrofit2.http.PUT;

public class MApplication extends Application implements Application.ActivityLifecycleCallbacks{

    private static MApplication app = null;
    public static MApplication getInstance() {
        return app;
    }
    public PictureResult pictureResult;
    public UAVInfoSN uavInfoSN;
    public MissionStatus missionStatus;
    public PayloadData payloadData;

    public MissionStatus getMissionStatus() {
        return missionStatus;
    }

    public void setMissionStatus(MissionStatus missionStatus) {
        this.missionStatus = missionStatus;
    }

    public void setPayloadData(PayloadData payloadData) {
        this.payloadData = payloadData;
    }

    public PayloadData getPayloadData() {
        return payloadData;
    }

    public String rtmpUrl;

    public String getRtmpUrl() {
        return rtmpUrl;
    }

    public void setRtmpUrl(String rtmpUrl) {
        this.rtmpUrl = rtmpUrl;
    }

    public UAVInfoSN getUavInfoSN(){
        return uavInfoSN;
    }

    public void setUAVInfoSN(UAVInfoSN uavInfoSN) {
        this.uavInfoSN = uavInfoSN;
    }

    public void setPictureResult(PictureResult pictureResult) {
        this.pictureResult = pictureResult;
    }

    public PictureResult getPictureResult() {
        return pictureResult;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        registerActivityLifecycleCallbacks(this);

        CrashReport.initCrashReport(getApplicationContext(), "e65353e492", true);
        ContextUtil.init(this);
        SpUtil.init(this);
        xLogInit();

        // 初始化DJI SDK - 关键修复
        initDJISDK();

        MQttManager.getInstance().init(this);
        MQttManager.getInstance().notifyAppStarted();

    }

    private void xLogInit(){
        LogConfiguration config = new LogConfiguration.Builder().build();
        Printer filePrinter = new FilePrinter
                .Builder("/sdcard/UAVLog/")
                .fileNameGenerator(new XlogFileNameGenerator())
                .flattener(new PatternFlattener("{d HH:mm:ss:SSS} {L}/{t}: {m}"))
                .build();
        XLog.init(BuildConfig.DEBUG ? LogLevel.ALL : LogLevel.NONE);       //Debug模式打印 正式版本不打印
        XLog.init(config, filePrinter);
    }

    /**
     * 初始化DJI SDK - 修复连接问题的关键方法
     */
    private void initDJISDK() {
        // 使用DJIHelper作为回调处理器，确保统一的状态管理
        DJISDKManager.getInstance().registerApp(this, DJIHelper.getInstance());
    }

    @Override
    protected void attachBaseContext(Context paramContext) {
        super.attachBaseContext(paramContext);
        app = this;
        Helper.install(this);
        MultiDex.install(this);
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        ContextUtil.addActivity(activity);
    }

    @Override
    public void onActivityStarted(Activity activity) {
    }

    @Override
    public void onActivityResumed(Activity activity) {
        ContextUtil.setCurrentActivity(activity);
    }

    @Override
    public void onActivityPaused(Activity activity) {
    }

    @Override
    public void onActivityStopped(Activity activity) {
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        ContextUtil.removeActivity(activity);
    }
}

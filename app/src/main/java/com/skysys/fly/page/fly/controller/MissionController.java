package com.skysys.fly.page.fly.controller;


import static com.skysys.fly.common.drone.mission.BaseMissionHelper.EXECUTION_PAUSED;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.MISSION_FINISH;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.MISSION_PAUSE;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.MISSION_RESUME;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.MISSION_START;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.MISSION_STOP;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.MISSION_UPLOAD;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.READY_TO_EXECUTE;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.UNKNOWN;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.VALUE_FAILED;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.VALUE_FINISHED;
import static com.skysys.fly.common.drone.mission.BaseMissionHelper.VALUE_SUCCEED;

import android.app.AlertDialog;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.skysys.fly.MApplication;
import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.GlobalController;
import com.skysys.fly.common.data.database.WaypointTable;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.FlightControllerKey;
import com.skysys.fly.common.drone.key.GimbalKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.drone.mission.BaseMissionHelper;
import com.skysys.fly.common.drone.mission.MissionOperatorFactory;
import com.skysys.fly.common.drone.mission.MissionUtil;
import com.skysys.fly.common.drone.mission.WaypointV2MissionHelper;
import com.skysys.fly.common.json.JsonUtil;
import com.skysys.fly.common.lbs.MapController;
import com.skysys.fly.common.lbs.MissionMapPainter;
import com.skysys.fly.common.lbs.bean.AppLatLng;
import com.skysys.fly.common.listener.YNListener0;
import com.skysys.fly.data.mission.MissionDetail;
import com.skysys.fly.data.mission.MissionStatus;
import com.skysys.fly.data.mission.PayloadData;
import com.skysys.fly.data.preference.SpUtil;
import com.skysys.fly.databinding.ActivityAircraftBinding;
import com.skysys.fly.net.bean.Flightpath;
import com.skysys.fly.net.bean.Mission;
import com.skysys.fly.net.bean.MissionJson;
import com.skysys.fly.net.bean.MqttInfo;
import com.skysys.fly.net.bean.UAVInfoSN;
import com.skysys.fly.net.bean.ZKYFlightPath;
import com.skysys.fly.net.bean.ZKYMissionJson;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.page.fly.setting.pager.detail.BatteryInfo;
import com.skysys.fly.page.fly.view.AutoTakeOffDialog;
import com.skysys.fly.page.plan.BaseMission;
import com.skysys.fly.page.plan.WaypointMission;
import com.skysys.fly.util.LatLngUtil;
import com.skysys.fly.util.PSDKLogger;
import com.skysys.fly.util.ToastUtil;
import com.skysys.fly.util.coordinate.GeoSysConversion;
import com.skysys.fly.view.adapter.CommonAdapter;
import com.skysys.fly.view.adapter.EasyAdapter;
import com.skysys.fly.view.adapter.EasyHolder;
import com.skysys.fly.view.adapter.ViewHolder;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import dji.common.gimbal.GimbalMode;
import dji.common.gimbal.Rotation;
import dji.common.gimbal.RotationMode;
import dji.common.mission.waypoint.WaypointAction;
import dji.common.mission.waypoint.WaypointActionType;
import dji.common.perception.POSAngle;
import dji.sdk.payload.Payload;
import dji.sdk.sdkmanager.DJISDKManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


public class MissionController implements BaseMissionHelper.IOperationResultListener, View.OnClickListener {

    private static final int ENABLE_NONE = 0x0000;
    private static final int ENABLE_START_BUTTON = 0x0001;
    private static final int ENABLE_PAUSE_BUTTON = 0x0002;
    private static final int ENABLE_STOP_BUTTON = 0x0004;

    private int lastShowToastID;
    private long lastUploadTime;
    private long lastShowToastTime;

    private boolean isFlying;
    private boolean lastFly;
    private boolean isClickStartMission;

    private AircraftActivity activity;
    private ActivityAircraftBinding binding;
    private RtmpController rtmpController;

    private MissionDetail missionDetail = new MissionDetail();
    private MissionStatus missionStatus = new MissionStatus();
    private PayloadData payloadData = new PayloadData();
    private List<String> missionDetailsList = new ArrayList<>();
    private List<DensityData> densityDetailList = new ArrayList<>();
    private EasyAdapter missionDetailAdapter;
    private EasyAdapter densityDetailAdapter;
    private WaypointMission mChosenMission;
    private MissionMapPainter mMissionMapPainter;
    private BaseMissionHelper missionHelper;
    private KeyListener isFlyingListener;
    private Payload payload = null;

    private boolean autoFly;
    private boolean isH20;
    private int currentWaypointIndex = -1;
    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm:ss");

    public MissionController(AircraftActivity activity, RtmpController controller) {
        this.activity = activity;
        this.binding = activity.getBinding();
        this.rtmpController = controller;
        binding.buttonLoad.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initMission(binding.spinnerMission.getSelectedItemPosition());
            }
        });
        binding.missionDetail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                /*if(mChosenMission == null){
                    ToastUtil.show("暂无任务");
                    return;
                }*/
                binding.rlMissionDetail.setVisibility(View.VISIBLE);
            }
        });
        binding.closeMissionDetail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.rlMissionDetail.setVisibility(View.GONE);
            }
        });

        binding.densityDetail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.rlDensityDetail.setVisibility(View.VISIBLE);
            }
        });
        binding.closeDensityDetail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.rlDensityDetail.setVisibility(View.GONE);
            }
        });

        ContextUtil.getHandler().post(densityRunnable);
    }

    Runnable densityRunnable = new Runnable() {
        @Override
        public void run() {
           /* Random random = new Random();
            int data = random.nextInt(800 - 300 + 1) + 300;*/
            float data = payloadData.getCO2Value1();
            binding.tvDensity.setText(String.valueOf(data));
            if (data < 380) {
                binding.tvDensity.setTextColor(Color.parseColor("#0E29CC"));
            } else if (data < 400) {
                binding.tvDensity.setTextColor(Color.parseColor("#08FF99"));
            } else if (data < 425) {
                binding.tvDensity.setTextColor(Color.parseColor("#66FF33"));
            } else if (data < 450) {
                binding.tvDensity.setTextColor(Color.parseColor("#99FF33"));
            } else if (data < 475) {
                binding.tvDensity.setTextColor(Color.parseColor("#CBFF33"));
            } else if (data < 500) {
                binding.tvDensity.setTextColor(Color.parseColor("#FEFF00"));
            } else if (data < 550) {
                binding.tvDensity.setTextColor(Color.parseColor("#FE9A33"));
            } else if (data < 600) {
                binding.tvDensity.setTextColor(Color.parseColor("#FE6700"));
            } else if (data < 650) {
                binding.tvDensity.setTextColor(Color.parseColor("#FE0200"));
            } else if (data < 700) {
                binding.tvDensity.setTextColor(Color.parseColor("#CB0100"));
            } else if (data < 750) {
                binding.tvDensity.setTextColor(Color.parseColor("#A40021"));
            } else if (data < 800) {
                binding.tvDensity.setTextColor(Color.parseColor("#800000"));
            }
            /*DensityData densityData = new DensityData();
            densityData.setDensity(data);
            densityData.setTime(System.currentTimeMillis());
            densityDetailList.add(densityData);
            //刷新列表
            initDensityDetailRecycleView();
            //获取下一次数据*/
            ContextUtil.getHandler().postDelayed(densityRunnable, 1000);
        }
    };

    class DensityData {
        int density;
        long time;

        public int getDensity() {
            return density;
        }

        public void setDensity(int density) {
            this.density = density;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }
    }

    public void onConnect(boolean connect) {
        if (connect) {
            isFlyingListener = new KeyListener<Boolean>() {
                @Override
                protected void onValueChanged(@Nullable Boolean old, @Nullable Boolean now) {
                    if (now != null) {
                        isFlying = now;
                        if (!isFlying && lastFly && mChosenMission != null) {
                            //测试发现御2行业进阶最后一个点currentWaypointIndex不更新.
                           /* if (currentWaypointIndex == mChosenMission.getWaypointList().size() || currentWaypointIndex == (mChosenMission.getWaypointList().size() - 1)) {
                                missionDetail.setComplete(true);
                                SpUtil.setMissionData(mChosenMission.getId(),missionDetail);
                                new AlertDialog.Builder(activity)
                                        .setMessage("任务已经完成，是否立即下载上传图片？")
                                        .setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
                                            ToastUtil.show("稍后可以到首页最近任务中处理");
                                        })
                                        .setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
                                            rtmpController.stopRtmp();
                                            MissionPhotoUploader.getInstance().onMissionComplete(mChosenMission.getId(), binding);
                                        }).create().show();
                            } else {
                                if(currentWaypointIndex > 0){//可能还没飞到第一个点就返回了，这个时候不做保存
                                    ToastUtil.show("任务还未完成，进行保存");
                                }
                            }*/
                        }
                        lastFly = now;
                    }
                }
            };
            KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.create(FlightControllerKey.IS_FLYING), isFlyingListener);


            payload = DJISDKManager.getInstance().getProduct().getPayload();
            if(payload == null){
                PSDKLogger.logConnection("GET_PAYLOAD", false, "Payload对象获取失败");
                ToastUtil.show("没有获取到payload");
            }else {
                PSDKLogger.logConnection("GET_PAYLOAD", true, "Payload对象获取成功");
                try {
                    payload.setCommandDataCallback(new Payload.CommandDataCallback() {
                    @Override
                    public void onGetCommandData(byte[] bytes) {
                        PSDKLogger.logDataReceive(bytes, bytes != null ? bytes.length : 0);
                        if(bytes != null){
                            if(bytes.length >= 32 && bytes.length < 48){
                                PSDKLogger.logDataParse("DATA_CATEGORY", bytes.length, true, "数据长度32-47，处理CO2和温度");
                                byte[] bytesCo2 = new byte[4];
                                byte[] bytesTem3 = new byte[4]; //这个温度是需要传的，之前的两个温度作废
                                System.arraycopy(bytes, 24, bytesCo2, 0, 4);
                                System.arraycopy(bytes, 28, bytesTem3, 0, 4);

                                try {
                                    float co2Value = getFloat(bytesCo2);
                                    payloadData.setCO2Value1(co2Value);
                                    payloadData.setCO2Value2(co2Value);
                                    PSDKLogger.logDataParse("CO2", co2Value, true, "CO2数据解析成功");
                                } catch (Exception e) {
                                    PSDKLogger.logError("PARSE_CO2", e);
                                }

                                try {
                                    float tempValue = getFloat(bytesTem3);
                                    payloadData.setTemperature1(tempValue);
                                    payloadData.setTemperature2(tempValue);
                                    PSDKLogger.logDataParse("TEMPERATURE", tempValue, true, "温度数据解析成功");
                                } catch (Exception e) {
                                    PSDKLogger.logError("PARSE_TEMPERATURE", e);
                                }

                                try {
                                    MApplication.getInstance().setPayloadData(payloadData);
                                    PSDKLogger.logDataStore("UPDATE_PAYLOAD_DATA", true, "短数据包存储成功");
                                } catch (Exception e) {
                                    PSDKLogger.logError("STORE_PAYLOAD_DATA", e);
                                }
                            }else if(bytes.length >= 48){
                                PSDKLogger.logDataParse("DATA_CATEGORY", bytes.length, true, "数据长度>=48，处理湿度和压力");
                                   /* byte[] bytesTem1 = new byte[4];
                                    byte[] bytesTem2 = new byte[4];*/
                                byte[] bytesHumidity1  = new byte[4];
                                byte[] bytesHumidity2  = new byte[4];
                                byte[] bytesPressure1  = new byte[4];
                                byte[] bytesPressure2  = new byte[4];
                                //System.arraycopy(bytes, 24, bytesTem1, 0, 4);
                                System.arraycopy(bytes, 28, bytesHumidity1, 0, 4);
                                System.arraycopy(bytes, 32, bytesPressure1, 0, 4);
                                //System.arraycopy(bytes, 36, bytesTem2, 0, 4);
                                System.arraycopy(bytes, 40, bytesHumidity2, 0, 4);
                                System.arraycopy(bytes, 44, bytesPressure2, 0, 4);

                                try {
                                    float humidity1 = getFloat(bytesHumidity1);
                                    float humidity2 = getFloat(bytesHumidity2);
                                    payloadData.setHumidity1(humidity1);
                                    payloadData.setHumidity2(humidity2);
                                    PSDKLogger.logDataParse("HUMIDITY", humidity1, true, "湿度1: " + humidity1 + ", 湿度2: " + humidity2);
                                } catch (Exception e) {
                                    PSDKLogger.logError("PARSE_HUMIDITY", e);
                                }

                                try {
                                    float pressure1 = getFloat(bytesPressure1);
                                    float pressure2 = getFloat(bytesPressure2);
                                    payloadData.setPressure1(pressure1);
                                    payloadData.setPressure2(pressure2);
                                    PSDKLogger.logDataParse("PRESSURE", pressure1, true, "压力1: " + pressure1 + ", 压力2: " + pressure2);
                                } catch (Exception e) {
                                    PSDKLogger.logError("PARSE_PRESSURE", e);
                                }

                                try {
                                    MApplication.getInstance().setPayloadData(payloadData);
                                    PSDKLogger.logDataStore("UPDATE_PAYLOAD_DATA", true, "长数据包存储成功");
                                } catch (Exception e) {
                                    PSDKLogger.logError("STORE_PAYLOAD_DATA", e);
                                }
                            }
                        } else {
                            PSDKLogger.logDataReceive(null, 0, "接收到空数据");
                        }
                        /*if(bytes != null && bytes.length == 64){
                            byte[] bytesCo2 = new byte[4];
                            byte[] bytesTem1 = new byte[4];
                            byte[] bytesTem2 = new byte[4];
                            byte[] bytesHumidity1  = new byte[4];
                            byte[] bytesHumidity2  = new byte[4];
                            byte[] bytesPressure1  = new byte[4];
                            byte[] bytesPressure2  = new byte[4];
                            System.arraycopy(bytes, 16, bytesCo2, 0, 4);
                            System.arraycopy(bytes, 20, bytesTem1, 0, 4);
                            System.arraycopy(bytes, 32, bytesTem2, 0, 4);
                            System.arraycopy(bytes, 24, bytesHumidity1, 0, 4);
                            System.arraycopy(bytes, 36, bytesHumidity2, 0, 4);
                            System.arraycopy(bytes, 28, bytesPressure1, 0, 4);
                            System.arraycopy(bytes, 40, bytesPressure2, 0, 4);
                            payloadData.setCO2Value1(getFloat(bytesCo2));
                            payloadData.setCO2Value2(getFloat(bytesCo2));
                            payloadData.setHumidity1(getFloat(bytesHumidity1));
                            payloadData.setHumidity2(getFloat(bytesHumidity2));
                            payloadData.setPressure1(getFloat(bytesPressure1));
                            payloadData.setPressure2(getFloat(bytesPressure2));
                            payloadData.setTemperature1(getFloat(bytesTem1));
                            payloadData.setTemperature2(getFloat(bytesTem2));
                            MApplication.getInstance().setPayloadData(payloadData);
                        }*/
                    }
                    });
                    PSDKLogger.logConnection("SET_CALLBACK", true, "CommandDataCallback设置成功");
                } catch (Exception e) {
                    PSDKLogger.logError("SET_CALLBACK", e);
                }
            }

            //addPhotoListener();
            //isH20 = DJIHelper.getInstance().isH20();

        } else {
            if (isFlyingListener != null) {
                KeyManager.getInstance().removeListener(isFlyingListener);
                setLayoutGroupEnable(ENABLE_START_BUTTON);
            }
            if(payload != null){
                try {
                    payload.setCommandDataCallback(null);
                    PSDKLogger.logConnection("CLEAR_CALLBACK", true, "CommandDataCallback清理成功");
                } catch (Exception e) {
                    PSDKLogger.logError("CLEAR_CALLBACK", e);
                }
            }
            /*if (DJIHelper.getInstance().getCamera() != null) {
                DJIHelper.getInstance().getCamera().setMediaFileCallback(null);
            }*/
        }
    }


    public void onPlanMission(String planMission) {
        if (DJIHelper.getInstance().getFlightController() == null) {
            ToastUtil.show("无人机未连接");
            return;
        }
    }

    private void initDensityDetailRecycleView() {
        if (densityDetailAdapter == null) {
            densityDetailAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), densityDetailList) {
                @Override
                public EasyHolder getHolder(int type) {
                    return new EasyHolder() {

                        private TextView tv_text_item;

                        @Override
                        public int getLayout() {
                            return R.layout.one_center_item;
                        }

                        @Override
                        public View createView(int position) {
                            tv_text_item = view.findViewById(R.id.tv_center_item);
                            return view;
                        }

                        @Override
                        public void refreshView(int position, Object item) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                            DensityData ite = (DensityData) item;
                            tv_text_item.setText("气体浓度:" + ite.density + "  " + simpleDateFormat.format(ite.getTime()));
                            if (ite.density < 380) {
                                tv_text_item.setTextColor(Color.parseColor("#0E29CC"));
                            } else if (ite.density < 400) {
                                tv_text_item.setTextColor(Color.parseColor("#08FF99"));
                            } else if (ite.density < 425) {
                                tv_text_item.setTextColor(Color.parseColor("#66FF33"));
                            } else if (ite.density < 450) {
                                tv_text_item.setTextColor(Color.parseColor("#99FF33"));
                            } else if (ite.density < 475) {
                                tv_text_item.setTextColor(Color.parseColor("#CBFF33"));
                            } else if (ite.density < 500) {
                                tv_text_item.setTextColor(Color.parseColor("#FEFF00"));
                            } else if (ite.density < 550) {
                                tv_text_item.setTextColor(Color.parseColor("#FE9A33"));
                            } else if (ite.density < 600) {
                                tv_text_item.setTextColor(Color.parseColor("#FE6700"));
                            } else if (ite.density < 650) {
                                tv_text_item.setTextColor(Color.parseColor("#FE0200"));
                            } else if (ite.density < 700) {
                                tv_text_item.setTextColor(Color.parseColor("#CB0100"));
                            } else if (ite.density < 750) {
                                tv_text_item.setTextColor(Color.parseColor("#A40021"));
                            } else if (ite.density < 800) {
                                tv_text_item.setTextColor(Color.parseColor("#800000"));
                            }
                        }
                    };
                }
            };

            ContextUtil.getHandler().post(() -> {
                binding.rvDensityDetail.setAdapter(densityDetailAdapter);
            });
        } else {
            densityDetailAdapter.updateData(densityDetailList);
            binding.rvDensityDetail.setSelection(binding.rvDensityDetail.getBottom());
        }
    }

    private void initMissionDetaiRecyclerView() {
        if (missionDetailAdapter == null) {
            missionDetailAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), missionDetailsList) {
                @Override
                public EasyHolder getHolder(int type) {
                    return new EasyHolder() {

                        private TextView tv_text_item;

                        @Override
                        public int getLayout() {
                            return R.layout.one_center_item;
                        }

                        @Override
                        public View createView(int position) {
                            tv_text_item = view.findViewById(R.id.tv_center_item);
                            return view;
                        }

                        @Override
                        public void refreshView(int position, Object item) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                            String ite = (String) item;
                            tv_text_item.setText(ite);
                        }
                    };
                }
            };

            ContextUtil.getHandler().post(() -> {
                binding.rvMissionDetail.setAdapter(missionDetailAdapter);
            });
        } else {
            missionDetailAdapter.updateData(missionDetailsList);
        }

    }

    static class MissionViewHolder {
        TextView tv_text;
    }


    public void onRemoteMission(ZKYMissionJson missionJson) {
        autoFly = true;
        missionDetailsList.clear();
        List<ZKYFlightPath> ZkyFlightPaths = missionJson.getFlightpath();
        List<Flightpath> flightPaths = new ArrayList<>();

        for (int i = 0; i < ZkyFlightPaths.size(); i++) {
            Flightpath flightpath1 = new Flightpath();
            flightpath1.setAltitude((float) ZkyFlightPaths.get(i).getAltitude());
            flightpath1.setLongitude(ZkyFlightPaths.get(i).getLongitude());
            flightpath1.setLatitude(ZkyFlightPaths.get(i).getLatitude());
            flightPaths.add(flightpath1);
        }
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setHeadingMode(WaypointMission.HeadingMode.Auto);
        if (missionJson.getIsGoHomeMission() == 1) {
            waypointMission.setFinishedAction(BaseMission.FinishedAction.GoHome);
        } else {
            waypointMission.setFinishedAction(BaseMission.FinishedAction.NoAction);
        }

        missionDetailsList.add("航线速度:" + SpUtil.getSpeed() + "m/s");
        waypointMission.setFlySpeed(SpUtil.getSpeed());
        waypointMission.setId(System.currentTimeMillis() + "");
        waypointMission.setName(System.currentTimeMillis() + "");
        for (int i = 0; i < flightPaths.size(); i++) {
            Flightpath flightpath = flightPaths.get(i);
            WaypointMission.Waypoint waypoint = new WaypointMission.Waypoint();
            //waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            //double[] position = GeoSysConversion.gcj02toWGS84(flightpath.getLatitude(), flightpath.getLongitude());
            waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            waypoint.setGimbalPitch((short) -90);
            //waypoint.setHeading((short) (flightpath.getHeading() >= 0 && flightpath.getHeading() < 360 ? flightpath.getHeading() : 0));
            missionDetailsList.add("航点" + (i + 1) + "的高度:" + flightpath.getAltitude() + "m");
            waypoint.setAltitude(flightpath.getAltitude());
            waypoint.setTurnMode(BaseMission.TurnMode.Clockwise);
            //waypoint.addWaypointAction(new WaypointMission.Waypoint.Action(WaypointMission.Waypoint.ActionType.StartTakePhoto, 0));
            waypointMission.addWaypoint(waypoint);
        }

        //地图上清除上一条任务
        if (mMissionMapPainter != null) {
            mMissionMapPainter.remove(mChosenMission);
        }
        this.mChosenMission = waypointMission;
        if (autoFly && mChosenMission != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    initMissionDetaiRecyclerView();
                    initUI(activity.getMapController());
                    ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 10);
                }
            });
        }
    }

    private void initMission(int i) {
        autoFly = true;
        List<Flightpath> flightPaths = new ArrayList<>();
        switch (i) {
            case 0:
                Flightpath flightpath1 = new Flightpath();
                flightpath1.setAltitude(175);
                flightpath1.setLongitude(116.39777672526593);
                flightpath1.setLatitude(39.99591849829794);
                flightPaths.add(flightpath1);

                Flightpath flightpath2 = new Flightpath();
                flightpath2.setAltitude(175);
                flightpath2.setLongitude(116.39764759507692);
                flightpath2.setLatitude(39.99591850632887);
                flightPaths.add(flightpath2);

                Flightpath flightpath3 = new Flightpath();
                flightpath3.setAltitude(175);
                flightpath3.setLongitude(116.39738931466763);
                flightpath3.setLatitude(39.99572067120841);
                flightPaths.add(flightpath3);

                Flightpath flightpath4 = new Flightpath();
                flightpath4.setAltitude(11);
                flightpath4.setLongitude(116.39738916535013);
                flightpath4.setLatitude(39.99424578378125);
                flightPaths.add(flightpath4);

                break;
            case 1:
                Flightpath flightpath11 = new Flightpath();
                flightpath11.setAltitude(803);
                flightpath11.setLongitude(116.39213023713879);
                flightpath11.setLatitude(39.99686300329385);
                flightPaths.add(flightpath11);

                Flightpath flightpath22 = new Flightpath();
                flightpath22.setAltitude(803);
                flightpath22.setLongitude(116.39213020967465);
                flightpath22.setLatitude(39.99572985807068);
                flightPaths.add(flightpath22);

                Flightpath flightpath33 = new Flightpath();
                flightpath33.setAltitude(953);
                flightpath33.setLongitude(116.39213017698101);
                flightpath33.setLatitude(39.99438087566215);
                flightPaths.add(flightpath33);

                Flightpath flightpath44 = new Flightpath();
                flightpath44.setAltitude(959);
                flightpath44.setLongitude(116.39205974391798);
                flightpath44.setLatitude(39.99438087664267);
                flightPaths.add(flightpath44);

                break;
            case 2:
                Flightpath flightpath1_3 = new Flightpath();
                flightpath1_3.setAltitude(561);
                flightpath1_3.setLongitude(116.39090933075936);
                flightpath1_3.setLatitude(39.993364653208445);
                flightPaths.add(flightpath1_3);

                Flightpath flightpath2_3 = new Flightpath();
                flightpath2_3.setAltitude(561);
                flightpath2_3.setLongitude(116.39221232303721);
                flightpath2_3.setLatitude(39.99336464104992);
                flightPaths.add(flightpath2_3);

                Flightpath flightpath3_3 = new Flightpath();
                flightpath3_3.setAltitude(561);
                flightpath3_3.setLongitude(116.39389101704977);
                flightpath3_3.setLatitude(39.994650633775514);
                flightPaths.add(flightpath3_3);

                Flightpath flightpath4_3 = new Flightpath();
                flightpath4_3.setAltitude(413);
                flightpath4_3.setLongitude(116.39389108362495);
                flightpath4_3.setLatitude(39.99598162975083);
                flightPaths.add(flightpath4_3);
                break;
            case 3:
                Flightpath flightpath1_4 = new Flightpath();
                flightpath1_4.setAltitude(411);
                flightpath1_4.setLongitude(116.40018357610425);
                flightpath1_4.setLatitude(39.99826555178249);
                flightPaths.add(flightpath1_4);

                Flightpath flightpath2_4 = new Flightpath();
                flightpath2_4.setAltitude(411);
                flightpath2_4.setLongitude(116.39578125926396);
                flightpath2_4.setLatitude(39.99826583579259);
                flightPaths.add(flightpath2_4);

                Flightpath flightpath3_4 = new Flightpath();
                flightpath3_4.setAltitude(395);
                flightpath3_4.setLongitude(116.39559342707805);
                flightpath3_4.setLatitude(39.99826584420586);
                flightPaths.add(flightpath3_4);

                Flightpath flightpath4_4 = new Flightpath();
                flightpath4_4.setAltitude(394);
                flightpath4_4.setLongitude(116.3955934277521);
                flightpath4_4.setLatitude(39.9982748374219);
                flightPaths.add(flightpath4_4);
                break;
            case 4:
                Flightpath flightpath1_5 = new Flightpath();
                flightpath1_5.setAltitude(747);
                flightpath1_5.setLongitude(116.39855152341629);
                flightpath1_5.setLatitude(39.9960623385593);
                flightPaths.add(flightpath1_5);

                Flightpath flightpath2_5 = new Flightpath();
                flightpath2_5.setAltitude(747);
                flightpath2_5.setLongitude(116.39961978450062);
                flightpath2_5.setLatitude(39.996062259512755);
                flightPaths.add(flightpath2_5);

                Flightpath flightpath3_5 = new Flightpath();
                flightpath3_5.setAltitude(747);
                flightpath3_5.setLongitude(116.4007935618981);
                flightpath3_5.setLatitude(39.99516283974185);
                flightPaths.add(flightpath3_5);

                Flightpath flightpath4_5 = new Flightpath();
                flightpath4_5.setAltitude(846);
                flightpath4_5.setLongitude(116.4007934273831);
                flightpath4_5.setLatitude(39.99427251136078);
                flightPaths.add(flightpath4_5);
                break;
            case 5:
                Flightpath flightpath1_6 = new Flightpath();
                flightpath1_6.setAltitude(462);
                flightpath1_6.setLongitude(116.39798861992077);
                flightpath1_6.setLatitude(40.001287434806564);
                flightPaths.add(flightpath1_6);

                Flightpath flightpath2_6 = new Flightpath();
                flightpath2_6.setAltitude(462);
                flightpath2_6.setLongitude(116.39539407304854);
                flightpath2_6.setLatitude(40.00128757340188);
                flightPaths.add(flightpath2_6);

                Flightpath flightpath3_6 = new Flightpath();
                flightpath3_6.setAltitude(461);
                flightpath3_6.setLongitude(116.39538233301742);
                flightpath3_6.setLatitude(40.00128757389754);
                flightPaths.add(flightpath3_6);

                Flightpath flightpath4_6 = new Flightpath();
                flightpath4_6.setAltitude(343);
                flightpath4_6.setLongitude(116.39538225675368);
                flightpath4_6.setLatitude(40.000226374404924);
                flightPaths.add(flightpath4_6);
                break;
            case 6:
                Flightpath flightpath1_7 = new Flightpath();
                flightpath1_7.setAltitude(364);
                flightpath1_7.setLongitude(116.40136903291734);
                flightpath1_7.setLatitude(39.996799552619365);
                flightPaths.add(flightpath1_7);

                Flightpath flightpath2_7 = new Flightpath();
                flightpath2_7.setAltitude(364);
                flightpath2_7.setLongitude(116.40136958671798);
                flightpath2_7.setLatitude(40.000270933980005);
                flightPaths.add(flightpath2_7);

                Flightpath flightpath3_7 = new Flightpath();
                flightpath3_7.setAltitude(729);
                flightpath3_7.setLongitude(116.40136958671798);
                flightpath3_7.setLatitude(40.000270933980005);
                flightPaths.add(flightpath3_7);

                Flightpath flightpath4_7 = new Flightpath();
                flightpath4_7.setAltitude(757);
                flightpath4_7.setLongitude(116.40104087074255);
                flightpath4_7.setLatitude(40.000270964289946);
                flightPaths.add(flightpath4_7);
                break;
            case 7:
                Flightpath flightpath1_8 = new Flightpath();
                flightpath1_8.setAltitude(286);
                flightpath1_8.setLongitude(116.40004224839748);
                flightpath1_8.setLatitude(39.99502800569638);
                flightPaths.add(flightpath1_8);

                Flightpath flightpath2_8 = new Flightpath();
                flightpath2_8.setAltitude(286);
                flightpath2_8.setLongitude(116.39794097544778);
                flightpath2_8.setLatitude(39.99502815948426);
                flightPaths.add(flightpath2_8);

                Flightpath flightpath3_8 = new Flightpath();
                flightpath3_8.setAltitude(286);
                flightpath3_8.setLongitude(116.39686106894739);
                flightpath3_8.setLatitude(39.99585559963792);
                flightPaths.add(flightpath3_8);

                Flightpath flightpath4_8 = new Flightpath();
                flightpath4_8.setAltitude(440);
                flightpath4_8.setLongitude(116.39505324796242);
                flightpath4_8.setLatitude(39.99585568480603);
                flightPaths.add(flightpath4_8);
                break;
            case 8:
                Flightpath flightpath1_9 = new Flightpath();
                flightpath1_9.setAltitude(887);
                flightpath1_9.setLongitude(116.40212033785633);
                flightpath1_9.setLatitude(39.996754513778505);
                flightPaths.add(flightpath1_9);

                Flightpath flightpath2_9 = new Flightpath();
                flightpath2_9.setAltitude(887);
                flightpath2_9.setLongitude(116.40212048507937);
                flightpath2_9.setLatitude(39.99761786250932);
                flightPaths.add(flightpath2_9);
                break;
        }

        missionDetailsList.clear();
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setHeadingMode(WaypointMission.HeadingMode.Auto);
        waypointMission.setFinishedAction(BaseMission.FinishedAction.NoAction);
        missionDetailsList.add("航线速度:" + SpUtil.getSpeed() + "m/s");
        waypointMission.setFlySpeed(SpUtil.getSpeed());
        waypointMission.setId(System.currentTimeMillis() + "");
        waypointMission.setName(System.currentTimeMillis() + "");
        for (int j = 0; j < flightPaths.size(); j++) {
            Flightpath flightpath = flightPaths.get(j);
            WaypointMission.Waypoint waypoint = new WaypointMission.Waypoint();
            //waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            //double[] position = GeoSysConversion.gcj02toWGS84(flightpath.getLatitude(), flightpath.getLongitude());
            waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            waypoint.setGimbalPitch((short) -90);
            //waypoint.setHeading((short) (flightpath.getHeading() >= 0 && flightpath.getHeading() < 360 ? flightpath.getHeading() : 0));
            missionDetailsList.add("航点" + (j + 1) + "的高度:" + flightpath.getAltitude() + "m");
            waypoint.setAltitude(flightpath.getAltitude());
            waypoint.setTurnMode(BaseMission.TurnMode.Clockwise);
            //waypoint.addWaypointAction(new WaypointMission.Waypoint.Action(WaypointMission.Waypoint.ActionType.StartTakePhoto, 0));
            waypointMission.addWaypoint(waypoint);
        }

        //地图上清除上一条任务
        if (mMissionMapPainter != null) {
            mMissionMapPainter.remove(mChosenMission);
        }

        this.mChosenMission = waypointMission;
        if (autoFly && mChosenMission != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    initMissionDetaiRecyclerView();
                    initUI(activity.getMapController());
                    ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 10);
                }
            });
        }
    }

    private void initMissionHelper() {
        if (mChosenMission != null) {
            if (missionHelper != null) {
                Log.e("TAG", "initMissionHelper: 再次接收任务，初始化helper");
                missionHelper.removeListener();
                missionHelper = null;
            }
            missionHelper = MissionOperatorFactory.getMissionHelper();
            missionHelper.setResultListener(this);
            missionHelper.setMissionProcessCallback(index -> {
                if (index > 0 && index > currentWaypointIndex) {
                    currentWaypointIndex = index;
                   /* ToastUtil.show("当前第几个点" + currentWaypointIndex);
                    //这是为了mqtt上报
                    missionStatus.setCurrentIndex(currentWaypointIndex);
                    MApplication.getInstance().setMissionStatus(missionStatus);
                    //这是为了断点续飞
                    missionDetail.setCurrentPosition(currentWaypointIndex);
                    SpUtil.setMissionData(mChosenMission.getId(), missionDetail);*/
                }
            });
        }
    }

    private void initUI(MapController mapController) {
        if (mChosenMission != null) {

            binding.viewMissionPanel.main.setVisibility(View.VISIBLE);
            mMissionMapPainter = new MissionMapPainter(activity, mapController);

            //activity.mMapController.getMap().clear();
            mMissionMapPainter.draw(mChosenMission, true);

            binding.viewMissionPanel.ivMissionStartLay.setOnClickListener(this);
            binding.viewMissionPanel.ivMissionPauseLay.setOnClickListener(this);
            binding.viewMissionPanel.ivMissionStopLay.setOnClickListener(this);

            addAircraftStatusListener();
            setLayoutGroupEnable(ENABLE_START_BUTTON);
        }
    }

    public void onDestroy() {
        if (mMissionMapPainter != null) {
            mMissionMapPainter.removeAll();
        }

        if (missionHelper != null) {
            missionHelper.removeListener();
        }

        if (isFlyingListener != null) {
            KeyManager.getInstance().removeListener(isFlyingListener);
        }
    }

    private void addAircraftStatusListener() {
        KeyListener altitudeListener = new KeyListener<Float>() {
            @Override
            protected void onValueChanged(@Nullable Float old, @Nullable Float now) {
                if (now != null) {
                    /*if (isClickStartMission && now >= 1) {
                        isClickStartMission = false;
                        uploadMission();
                    }*/
                    //MqttInfo.getInstance().setAltitude(now);
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.create(FlightControllerKey.ALTITUDE), altitudeListener);
    }

    private void uploadMission() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUploadTime < 2000) {
            return;
        } else {
            lastUploadTime = currentTime;
        }

        if (mChosenMission != null) {
            updateRemindText(R.string.fly_mission_upload_started);
            ContextUtil.getHandler().post(() -> binding.viewMissionPanel.ivMissionStart.updateProgress(0.01f));
            if (missionHelper instanceof WaypointV2MissionHelper) {
                missionHelper.setWayPointV2Mission(MissionUtil.parseDJIWayMission(mChosenMission));
                missionHelper.setOriginMission(mChosenMission);
            } else {
                missionHelper.setWayPointMission(MissionUtil.diyToDJI(mChosenMission));
            }

            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    prepareForUpload();
                }
            }, 1000);
        }
    }

    private void startMission() {
        if (readyForMission()) {
            int state = missionHelper.getCurrentState();
            if (state == EXECUTION_PAUSED) {
                missionHelper.resumeMission();
            } else if (state == READY_TO_EXECUTE) {
                missionHelper.startMission();
            }
        }
    }

    /*现在把起飞，上传任务和开始任务合并到一个按钮操作了，但是前提是飞机必须先起飞才能上传任务*/
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_mission_start_lay:

                if (!readyForStart()) {
                    return;
                }
                initMissionHelper();
                //setGimbalFollowMode();

               /* if (!isFlying) {
                    isClickStartMission = true;
                    if (mChosenMission != null) {
                        new AutoTakeOffDialog(autoFly);
                    }
                } else {*/
                //*判断是否是暂停状态*//*
                int state = missionHelper.getCurrentState();
                if (state == EXECUTION_PAUSED) {
                    missionHelper.resumeMission();
                } else {
                    uploadMission();
                }
                //}
                break;
            case R.id.iv_mission_pause_lay:
                missionHelper.pauseMission();
                break;
            case R.id.iv_mission_stop_lay:
                missionHelper.stopMission();
                break;
        }
    }

    private void resetGimbalMode(int value) {
        rotateGimbal(value);
        KeyManager.getInstance().setValue(GimbalKey.create(GimbalKey.MODE), GimbalMode.YAW_FOLLOW, null);
    }

    private void setGimbalFollowMode() {
        KeyManager.getInstance().setValue(GimbalKey.create(GimbalKey.MODE), GimbalMode.YAW_FOLLOW, new YNListener0() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("设置云台跟随成功");
            }

            @Override
            public void onException(Throwable e) {
                ToastUtil.show("设置云台跟随失败" + e.getMessage());
            }
        });
    }

    private void rotateGimbal(int value) {
        Rotation.Builder mRotationBuilder = new Rotation.Builder()
                .mode(RotationMode.ABSOLUTE_ANGLE)
                .pitch(value)
                .roll(Rotation.NO_ROTATION)
                .yaw(Rotation.NO_ROTATION)
                .time(0);
        KeyManager.getInstance().performAction(GimbalKey.create(GimbalKey.ROTATE), null, mRotationBuilder.build());
    }

    private boolean readyForMission() {
        // 设置返航点
        Boolean isHomeSet = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_HOME_LOCATION_SET));
        if (isHomeSet == null || !isHomeSet) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.set_home_first, false);
            return false;
        }
        // 需起飞无人机
        /*Boolean isFlying = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_FLYING));
        if (isFlying == null || !isFlying) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_first_route, false);
            return false;
        }*/

        return true;
    }

    private boolean readyForStart() {
        if (!DJIHelper.getInstance().isAircraftConnected()) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.please_check_connection, true);
            return false;
        }

        Boolean isLanding = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_LANDING));
        if (isLanding == null || isLanding) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_auto_home, false);
            return false;
        }

        Boolean isGoingHome = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_GOING_HOME));
        if (isGoingHome == null || isGoingHome) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_auto_home, false);
            return false;
        }

        return true;
    }

    private void prepareForUpload() {
        ContextUtil.getHandler().post(() -> setLayoutGroupEnable(ENABLE_NONE));
        missionHelper.stopLastAndUploadMission();
    }

    @Override
    public void onResult(int state, float value, String err) {
        switch (state) {
            case MISSION_UPLOAD:
                setLayoutGroupEnable(ENABLE_START_BUTTON);
                onResultUpload(value, err);
                break;
            case MISSION_START:
                currentWaypointIndex = -1;
                onResultStart(value, err);
                break;
            case MISSION_PAUSE:
                onResultPause(value, err);
                break;
            case MISSION_RESUME:
                onResultResume(value, err);
                break;
            case MISSION_STOP:
                //currentWaypointIndex = -1;
                onResultStop(value, err);
                break;
            case MISSION_FINISH:
                //currentWaypointIndex = -1;
                onResultMissionFinish(value, err);
                break;
            case UNKNOWN:
                setLayoutGroupEnable(ENABLE_START_BUTTON);
                onResultUnknown(value, err);
                break;
        }
    }

    private void onResultUpload(float value, String err) {
        if (value > 0 && value < 1) {
            binding.viewMissionPanel.ivMissionStart.updateProgress(value);
        } else if (value == VALUE_FINISHED) {
            setLayoutGroupEnable(ENABLE_NONE);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
            updateRemindText(R.string.fly_mission_upload_finished);
            updateRemindText(R.string.fly_mission_prepare);
            /*上传任务后直接开始任务*/
            ContextUtil.getHandler().postDelayed(this::startMission, 3000);

        } else if (value == VALUE_FAILED) {
            updateRemindText(R.string.fly_mission_upload_failed + ":" + err, true);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ALERT, "任务上传至无人机失败：" + err, "", false);
        }
    }


    private void onResultStart(float value, String err) {
        if (value == VALUE_SUCCEED) {
            missionDetail.setStartTime(System.currentTimeMillis());
            missionDetail.setWaypointMission(mChosenMission);
            SpUtil.setMissionBatch(mChosenMission.getId());
            SpUtil.setMissionData(mChosenMission.getId(), missionDetail);

            updateRemindText(R.string.fly_mission_start_succeed);
            setLayoutGroupEnable(ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON);
            MissionPhotoUploader.getInstance().setUploading(false);//每次开始前初始化上传状态，可能上一次上传失败了，不能影响下次任务执行
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_START);
        } else if (value == VALUE_FAILED) {
            updateRemindText(R.string.fly_mission_start_failed + ":" + err, true);
            setLayoutGroupEnable(ENABLE_START_BUTTON);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_START, "任务开始失败：" + err, "", false);
        }
    }

    private void onResultStop(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_START_BUTTON);
            updateRemindText(R.string.fly_mission_stop_succeed);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_STOP);
        } else if (value == VALUE_FAILED) {
            updateRemindText(R.string.fly_mission_stop_failed + ":" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_STOP, "任务结束失败：" + err, "", false);
        }
    }

    private void onResultPause(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_START_BUTTON | ENABLE_STOP_BUTTON);
            updateRemindText(R.string.fly_mission_pause_succeed);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_PAUSE);

        } else if (value == VALUE_FAILED) {
            updateRemindText(R.string.fly_mission_pause_failed + ":" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_PAUSE, "任务暂停失败：" + err, "", false);

        }
    }

    private void onResultResume(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON);
            updateRemindText(R.string.fly_mission_resume_succeed);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_RESUME);
        } else if (value == VALUE_FAILED) {
            updateRemindText(R.string.fly_mission_resume_failed + ":" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_RESUME, "任务恢复失败：" + err, "", false);
        }
    }

    private void onResultUnknown(float value, String err) {
        if (value == VALUE_FAILED) {
            updateRemindText("未知错误:" + err, true);
        }
    }

    private void onResultMissionFinish(float value, String err) {
        if (value == VALUE_SUCCEED) {
            //MissionDetail missionDetail = SpUtil.getMissionData(mChosenMission.getId());
            missionDetail.setEndTime(System.currentTimeMillis());
            SpUtil.setMissionData(mChosenMission.getId(), missionDetail);

            updateRemindText(R.string.mission_complete);
        } else if (value == VALUE_FAILED) {
            updateRemindText(R.string.fly_mission_resume_failed + ":" + err, true);
        }

        setLayoutGroupEnable(ENABLE_START_BUTTON);
    }

    private void setLayoutGroupEnable(int state) {
        boolean isStart = false, isPause = false, isStop = false;
        switch (state) {
            case ENABLE_START_BUTTON:
                isStart = true;
                break;
            case ENABLE_START_BUTTON | ENABLE_STOP_BUTTON:
                isStart = true;
                isStop = true;
                break;
            case ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON:
                isPause = true;
                isStop = true;
                break;
            default:
                break;
        }

        setLayoutEnable(binding.viewMissionPanel.ivMissionStartLay, isStart);
        setLayoutEnable(binding.viewMissionPanel.ivMissionPauseLay, isPause);
        setLayoutEnable(binding.viewMissionPanel.ivMissionStopLay, isStop);

        setImageButtonState(binding.viewMissionPanel.ivMissionStart, isStart);
        setImageButtonState(binding.viewMissionPanel.ivMissionPause, isPause);
        setImageButtonState(binding.viewMissionPanel.ivMissionStop, isStop);
    }

    private void setLayoutEnable(RelativeLayout layout, boolean enable) {
        layout.setClickable(enable);
    }

    private void setImageButtonState(ImageButton imageButton, boolean enable) {
        if (enable) {
            DrawableCompat.setTint(imageButton.getDrawable(), ContextUtil.getApplicationContext().getResources().getColor(R.color.icon_light_1));
        } else {
            DrawableCompat.setTint(imageButton.getDrawable(), ContextUtil.getApplicationContext().getResources().getColor(R.color.black_gray));
        }
    }

    private void updateRemindText(int id) {
        if (id == lastShowToastID && (System.currentTimeMillis() - lastShowToastTime) < 1500) {
            return;//解决同一条信息回调多次，显示多次的问题
        }

        lastShowToastID = id;
        lastShowToastTime = System.currentTimeMillis();
        updateRemindText(ContextUtil.getString(id), false);
    }

    private void updateRemindText(final String text, final boolean isWarning) {
        ContextUtil.getHandler().post(() -> PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(text, isWarning));
    }

    private float getFloat(byte[] b) {
        int accum = 0;
        accum = accum | (b[0] & 0xff) << 0;
        accum = accum | (b[1] & 0xff) << 8;
        accum = accum | (b[2] & 0xff) << 16;
        accum = accum | (b[3] & 0xff) << 24;
        System.out.println(accum);
        return Float.intBitsToFloat(accum);
    }
}

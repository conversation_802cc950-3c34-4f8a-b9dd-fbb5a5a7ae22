package com.skysys.fly.page.fly;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.core.widget.PopupWindowCompat;
import androidx.databinding.DataBindingUtil;

import com.google.gson.Gson;
import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.GlobalController;
import com.skysys.fly.common.SensorEventHelper;
import com.skysys.fly.common.drone.DJIActivateBindHelper;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.AirLinkKey;
import com.skysys.fly.common.drone.key.FlightControllerKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.event.Event;
import com.skysys.fly.common.event.Events;
import com.skysys.fly.common.lbs.MapController;
import com.skysys.fly.common.lbs.MapPainter;
import com.skysys.fly.common.lbs.MapServiceFactory;
import com.skysys.fly.common.lbs.amap.AMapController;
import com.skysys.fly.common.lbs.bean.AppLatLng;
import com.skysys.fly.common.listener.MyOrientationListener;
import com.skysys.fly.data.preference.SpUtil;
import com.skysys.fly.databinding.ActivityAircraftBinding;
import com.skysys.fly.net.bean.MissionJson;
import com.skysys.fly.net.bean.StationInfo;
import com.skysys.fly.page.ConnectActivity;
import com.skysys.fly.page.fly.controller.SecondaryVideoController;
import com.skysys.fly.page.fly.setting.pager.detail.GimbalAutoCalibratiomDialog;
import com.skysys.fly.page.fly.setting.pager.detail.GimbalRollSetDialog;
import com.skysys.fly.page.fly.view.djiux.DJIFPVOverlayWidget;
import com.skysys.fly.page.fly.view.djiux.DJIFpvWidget;
import com.skysys.fly.util.ToastUtil;
import com.skysys.fly.util.phone.DensityUtil;
import com.skysys.fly.util.phone.ImmerseUtil;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONObject;

import java.io.IOException;
import java.util.List;

import dji.common.airlink.OcuSyncFrequencyBand;
import dji.sdk.airlink.AirLink;
import dji.thirdparty.rx.subscriptions.CompositeSubscription;
import dji.ux.widget.FPVWidget;
import dji.ux.widget.RadarWidget;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;


public class AircraftActivity extends ConnectActivity implements View.OnSystemUiVisibilityChangeListener, View.OnClickListener {

    public static final int REQUEST_CODE_DJI_LOGIN = 1;
    public static final int SMALL_WINDOW_RATIO = 4;

    // the big one
    public int currentIndex = Events.IndexEvent.INDEX_LIVE;
    public MapController mMapController;
    protected View mapView;
    private View videoView;
    private PopupWindow mPopupWindow;
    private int bigWidth;
    private int bigHeight;
    private int smallWidth;
    private int smallHeight;
    private boolean isFirstRun;
    private List<StationInfo.Data.LocationInfo> locationList;
    private Dialog mDialog;
    private RadarWidget radarWidget;
    private DJIFpvWidget fpvWidget;
    private DJIFPVOverlayWidget fpvOverlayWidget;
    private MapPainter mMapPainter;
    private SensorEventHelper mSensorHelper;
    private ActivityAircraftBinding binding;
    private AircraftPresenter aircraftPresenter;
    private MyOrientationListener myOrientationListener;
    private AircraftFragmentManager mAircraftFragmentManager;
    private CompositeSubscription subscription = new CompositeSubscription();
    private Runnable enterImmerseMode = () -> ImmerseUtil.startImmerse(AircraftActivity.this);
    private boolean autoFly;

    public static void startActivity(Activity activity) {
        Intent intent = new Intent(activity, AircraftActivity.class);
        activity.startActivity(intent);
    }

    public static void startActivity(Activity activity, String missionString, boolean autoFly) {
        Intent intent = new Intent(activity, AircraftActivity.class);
        intent.putExtra("mission", missionString);
        //intent.putExtra("autoFly", autoFly);
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        getWindow().setNavigationBarColor(getResources().getColor(android.R.color.transparent));
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(this);

        ImmerseUtil.startImmerse(this);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_aircraft_main);

        //autoFly = getIntent().getBooleanExtra("autoFly", false);
        isFirstRun = true;
        initMap(savedInstanceState);
        ContextUtil.getHandler().postDelayed(() -> {
            isFirstRun = false;
            initUIDelay();
        }, 200);

        setFrequency();
    }

    private void setFrequency() {
        AirLink airLink = DJIHelper.getInstance().getAirLink();
        if (airLink != null) {
            if (airLink.getOcuSyncLink() != null) {
                airLink.getOcuSyncLink().setFrequencyBand(OcuSyncFrequencyBand.FREQUENCY_BAND_5_DOT_8_GHZ, djiError -> {
                    if (djiError == null) {
                        ContextUtil.getHandler().post(() -> {
                            ToastUtil.show("频段切换到5.8G");
                        });
                    } else {
                        ToastUtil.show("切换5.8G失败:" + djiError.getDescription());
                    }
                });
            }else {
                ToastUtil.show("没有获取到OcuSyncLink");
            }
        } else {
            ToastUtil.show("没有获取到airLink");
        }
    }

    private void initUIDelay() {

        binding = DataBindingUtil.setContentView(AircraftActivity.this, R.layout.activity_aircraft);

        initUI();

        mAircraftFragmentManager = new AircraftFragmentManager(getSupportFragmentManager(), binding);
        aircraftPresenter = new AircraftPresenter(AircraftActivity.this);

        myOrientationListener = new MyOrientationListener(AircraftActivity.this, AircraftActivity.this);
        myOrientationListener.enable();

        onUpdateAircraftState();
        DJIActivateBindHelper.getInstance().check();


        String[] missions = new String[]{"任务1", "任务2", "任务3", "任务4", "任务5", "任务6", "任务7", "任务8", "任务9"};
        binding.spinnerMission.setData(missions);
        binding.spinnerMission.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                //ToastUtil.show(binding.spinnerMission.getSelectedItemPosition() + "");
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    @Override
    public void onDetachedFromWindow() {
        if (this.subscription != null && !this.subscription.isUnsubscribed()) {
            this.subscription.unsubscribe();
        }

        super.onDetachedFromWindow();
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
    }


    @Override
    protected void onStart() {
        super.onStart();
        mMapPainter.onStart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMapController.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapController.onPause();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mMapPainter.onStop();
    }

    @Override
    protected void onDestroy() {
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        super.onDestroy();

        if (myOrientationListener != null)
            myOrientationListener.disable();

        mMapController.onDestroy();
        mMapPainter.onDestroy();

        mSensorHelper.release();

        if (aircraftPresenter != null)
            aircraftPresenter.onDestroy();

        DJIActivateBindHelper.getInstance().removeListener();
    }

    @Override
    public void onConnected() {
        super.onConnected();
        if (!isFirstRun) {
            onUpdateAircraftState();
        }
        if (isAirCraftConnected) {
            //addLandingListener();
        }
    }

    private void onUpdateAircraftState() {
        if (isAirCraftConnected) {
            initFpvWidget();
        } else {
            if (fpvWidget != null && fpvWidget.getCodecManager() != null) {
                fpvWidget.getCodecManager().setSurfaceToGray();
                GlobalController.getInstance().getMediaController().releaseVideoFeed();
            }
            DJIHelper.getInstance().resetGimbal();
        }

        if (fpvOverlayWidget != null) {
            fpvOverlayWidget.onConnected(isAirCraftConnected);
        }

        aircraftPresenter.onConnected(isAirCraftConnected);
    }

    private void initUI() {
        videoView = binding.panelFpv;

        initWindowSize();

        binding.panelFpv.bringChildToFront(binding.ivFpvBg);

        updateSmallWindowParams(mapView, smallWidth, smallHeight);
        binding.flyPageMain.addView(mapView);

        //动态更新组件位置
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.leftMargin = smallWidth + 30;
        lp.bottomMargin = DensityUtil.dp2px(5);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        binding.viewFlyParamPanel.main.setLayoutParams(lp);

        initAnimation();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mMapController.onSaveInstanceState(outState);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_DJI_LOGIN) {
            if (resultCode == RESULT_CANCELED) {
                finish();
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (mAircraftFragmentManager.onBackPressed()) {
            super.onBackPressed();
        }
    }

    @Override
    public void onSystemUiVisibilityChange(int visibility) {
        if (visibility == 0) {
            ContextUtil.getHandler().removeCallbacks(enterImmerseMode);
            ContextUtil.getHandler().postDelayed(enterImmerseMode, 2000);
        }
    }

    @Override
    public void onClick(View v) {
        binding.ivAnimation.startAnimation();
        animate();
    }

    public ActivityAircraftBinding getBinding() {
        return binding;
    }

    public FPVWidget getFpvWidget() {
        return fpvWidget;
    }

    public AircraftPresenter getAircraftPresenter() {
        return aircraftPresenter;
    }

    public MapController getMapController() {
        return this.mMapController;
    }

    public AircraftFragmentManager getAircraftFragmentManager() {
        return this.mAircraftFragmentManager;
    }

    public void dismissPopupWindow() {
        if (mPopupWindow != null) {
            mPopupWindow.dismiss();
        }
    }

    public void showPopup(View anchor, ListView listView, int length) {
        if (listView != null) {
            if (length == 3) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(690));
            } else if (length == 2) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(460));
            } else if (length <= 1) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(230));
            } else {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(900));
            }
            mPopupWindow.setFocusable(false);
            mPopupWindow.setBackgroundDrawable(new ColorDrawable());
            mPopupWindow.setOutsideTouchable(true);
            mPopupWindow.setFocusable(false);
            mPopupWindow.update();
            PopupWindowCompat.showAsDropDown(mPopupWindow, anchor, 0, -anchor.getHeight(), Gravity.CENTER);
            fullScreenImmersive(mPopupWindow.getContentView());
            mPopupWindow.setFocusable(true);
            mPopupWindow.update();
        }
    }

    public void showPopup1(View anchor, ListView listView, int length) {
        if (listView != null) {
            if (length == 3) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(600));
            } else if (length == 2) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(400));
            } else if (length <= 1) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(200));
            } else {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(800));
            }
            mPopupWindow.setFocusable(false);
            mPopupWindow.setBackgroundDrawable(new ColorDrawable());
            mPopupWindow.setOutsideTouchable(true);
            mPopupWindow.setFocusable(false);
            mPopupWindow.update();
            PopupWindowCompat.showAsDropDown(mPopupWindow, anchor, 0, anchor.getHeight(), Gravity.CENTER);
            fullScreenImmersive(mPopupWindow.getContentView());
            mPopupWindow.setFocusable(true);
            mPopupWindow.update();
        }
    }


    public void showNoviceDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.noive_mode_dialog, null);
        Button btnNovice = view.findViewById(R.id.noive_great);
        btnNovice.setOnClickListener(v -> mDialog.dismiss());
        builder.setView(view);
        builder.setCancelable(false);
        mDialog = builder.create();

        ImmerseUtil.showDialog(mDialog);
    }

    public void showGimbalAutoCalibrationDialog(int gimbalIndex) {
        new GimbalAutoCalibratiomDialog().calibrationDialog(this, gimbalIndex);
    }

    public void showGimbalRollSetDialog(int gimbalIndex) {
        GimbalRollSetDialog gimbalRollSetDialog = new GimbalRollSetDialog(this);
        gimbalRollSetDialog.setGimbalRollSetDialog(gimbalIndex);
    }

    private void initMap(Bundle savedInstanceState) {
        mMapController = MapServiceFactory.produceMapController();
        mapView = mMapController.getMapView(this);

        mSensorHelper = new SensorEventHelper(ContextUtil.getApplicationContext());
        mSensorHelper.init();

        mMapPainter = new MapPainter(this, mMapController, mSensorHelper, false);

        mMapController.onCreate(savedInstanceState);
        mMapPainter.onCreate();
    }

    private void initFpvWidget() {
        if (fpvWidget == null) {
            fpvWidget = new DJIFpvWidget(this);
            fpvWidget.setSourceCameraNameVisibility(false);
            fpvWidget.setSourceCameraSideVisibility(false);

            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            lp.addRule(RelativeLayout.CENTER_IN_PARENT);
            fpvWidget.setLayoutParams(lp);
            binding.panelFpv.addView(fpvWidget);
        } else {
            if (SecondaryVideoController.isShowSecondaryWidget()) {
                fpvWidget.setVideoSource(FPVWidget.VideoSource.PRIMARY);
            }
        }

        if (fpvOverlayWidget == null) {
            fpvOverlayWidget = new DJIFPVOverlayWidget(getApplicationContext());
            fpvOverlayWidget.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            binding.flyPageMain.addView(fpvOverlayWidget);
            updatePanelNormalLevel();
        }

        if (radarWidget == null) {
            updateRadarView();
        }
    }

    public void updateRadarView() {
        if (SpUtil.getRadarUIShow()) {
            if (radarWidget == null) {
                radarWidget = new RadarWidget(getApplicationContext());
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                lp.addRule(RelativeLayout.CENTER_IN_PARENT);
                radarWidget.setLayoutParams(lp);
                ContextUtil.getHandler().postDelayed(() -> {
                    radarWidget.setVisibility(View.VISIBLE);
                    binding.panelFpv.addView(radarWidget);
                    radarWidget.bringToFront();
                }, 500);
            } else {
                radarWidget.setVisibility(View.VISIBLE);
                radarWidget.bringToFront();
            }
        } else {
            if (radarWidget != null) {
                radarWidget.setVisibility(View.GONE);
            }
        }
    }

    private void fullScreenImmersive(View view) {
        int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        view.setSystemUiVisibility(uiOptions);
    }

    public void animate() {
        if (currentIndex == Events.IndexEvent.INDEX_LIVE) {
            updateSmallWindowParams(videoView, smallWidth, smallHeight);
            updateFullWindowParams(mapView);
        } else {
            updateFullWindowParams(videoView);
            updateSmallWindowParams(mapView, smallWidth, smallHeight);
        }

        updateViewLevel();
        currentIndex = currentIndex == Events.IndexEvent.INDEX_LIVE ? Events.IndexEvent.INDEX_MAP : Events.IndexEvent.INDEX_LIVE;

        Event.post(new Events.IndexEvent(currentIndex));
    }

    private void updateViewLevel() {
        if (currentIndex == Events.IndexEvent.INDEX_LIVE) {
//            binding.viewMapTool.main.setVisibility(View.VISIBLE);
            binding.viewGoHomePanel.main.setVisibility(View.GONE);
            binding.flyPageMain.bringChildToFront(mapView);
            binding.imageLevel.bringToFront();
            binding.imageLevel.setVisibility(View.VISIBLE);
            binding.flyPageMain.bringChildToFront(videoView);
        } else {
            if (fpvOverlayWidget != null) {
                binding.flyPageMain.bringChildToFront(fpvOverlayWidget);
            }

            binding.viewGoHomePanel.main.setVisibility(View.VISIBLE);
//            binding.viewMapTool.main.setVisibility(View.GONE);
            binding.flyPageMain.bringChildToFront(mapView);
            binding.flyPageMain.bringChildToFront(binding.panelNormalLive);
        }

        binding.flyPageMain.bringChildToFront(binding.panelNormalMap);
        binding.flyPageMain.bringChildToFront(binding.viewBaseStatusPanel.main);
        binding.flyPageMain.bringChildToFront(binding.ivAnimation);
    }

    private void initWindowSize() {
        bigWidth = DensityUtil.getScreenWidth();
        bigHeight = DensityUtil.getScreenHeight();

        smallWidth = bigWidth / SMALL_WINDOW_RATIO;
        smallHeight = smallWidth * 9 / 16;
    }

    private void initAnimation() {
        binding.ivAnimation.setParam(smallWidth, smallHeight, bigWidth, bigHeight);
        updateSmallWindowParams(binding.ivAnimation, smallWidth, smallHeight);
        binding.ivAnimation.setOnClickListener(this);
        binding.flyPageMain.bringChildToFront(binding.ivAnimation);
        //binding.flyPageMain.bringChildToFront(binding.viewShootRecorder.main);
    }

    private void updateSmallWindowParams(View view, int width, int height) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(width, height);
        lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        view.setLayoutParams(lp);
    }

    private void updateFullWindowParams(View view) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        view.setLayoutParams(lp);
    }

    private void updatePanelNormalLevel() {
        currentIndex = currentIndex == Events.IndexEvent.INDEX_LIVE ? Events.IndexEvent.INDEX_MAP : Events.IndexEvent.INDEX_LIVE;
        updateViewLevel();
        currentIndex = currentIndex == Events.IndexEvent.INDEX_LIVE ? Events.IndexEvent.INDEX_MAP : Events.IndexEvent.INDEX_LIVE;
        binding.containerNavigation.bringToFront();
    }



}

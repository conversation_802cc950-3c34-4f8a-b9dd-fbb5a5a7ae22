apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

repositories {
    mavenLocal()
    jcenter()
}

android {
    dataBinding {
        enabled = true
    }
    compileSdkVersion 33

    defaultConfig {
        applicationId "com.skysys.fly"
        versionCode 1
        versionName "1.0"
        minSdkVersion 23
        targetSdkVersion 34
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        ndk {
            // On x86 devices that run Android API 23 or above, if the application is targeted with API 23 or
            // above, FFmpeg lib might lead to runtime crashes or warnings.
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }

    signingConfigs {
        def appKeyAlias = System.getenv("KEY_ALIAS")
        def appKeyPassword = System.getenv("KEY_PASSWORD")
        def appStoreFile = System.getenv("STORE_FILE")
        def appStorePassword = System.getenv("STORE_PASSWORD")
        if (!appKeyAlias || !appKeyPassword || !appStoreFile || !appStorePassword) {
            appKeyAlias = "yunfei"
            appKeyPassword = "yunfei"
            appStoreFile = "yunfei_debug.jks"
            appStorePassword = "yunfei"
        }
        keystore_default {
            keyAlias appKeyAlias
            keyPassword appKeyPassword
            storeFile file(appStoreFile)
            storePassword appStorePassword
        }

        release {
            storeFile file("yunfei_release.jks")
            storePassword "yunfei123"
            keyAlias "yunfei123"
            keyPassword "yunfei123"
        }
    }

    buildTypes {
        release {
            //shrinkResources false
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.keystore_default
        }
    }

    dexOptions {
        javaMaxHeapSize "4g"
    }
    lintOptions{
        checkReleaseBuilds false
        abortOnError false
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all { output ->
            outputFileName = "SkysysFly" + "-" + buildType.name + "-" + defaultConfig.versionName + ".apk"
        }
    }
    packagingOptions{
        doNotStrip "*/*/libdjivideo.so"
        doNotStrip "*/*/libSDKRelativeJNI.so"
        doNotStrip "*/*/libFlyForbid.so"
        doNotStrip "*/*/libduml_vision_bokeh.so"
        doNotStrip "*/*/libyuv2.so"
        doNotStrip "*/*/libGroudStation.so"
        doNotStrip "*/*/libFRCorkscrew.so"
        doNotStrip "*/*/libUpgradeVerify.so"
        doNotStrip "*/*/libFR.so"
        doNotStrip "*/*/libDJIFlySafeCore.so"
        doNotStrip "*/*/libdjifs_jni.so"
        doNotStrip "*/*/libsfjni.so"
        doNotStrip "*/*/libDJICommonJNI.so"
        doNotStrip "*/*/libDJICSDKCommon.so"
        doNotStrip "*/*/libDJIUpgradeCore.so"
        doNotStrip "*/*/libDJIUpgradeJNI.so"
        doNotStrip "*/*/libDJIWaypointV2Core.so"
        doNotStrip "*/*/libAMapSDK_MAP_v6_9_2.so"
        doNotStrip "*/*/libDJIMOP.so"
        doNotStrip "*/*/libDJISDKLOGJNI.so"

        pickFirst 'lib/*/libstlport_shared.so'
        pickFirst 'lib/*/libRoadLineRebuildAPI.so'
        pickFirst 'lib/*/libGNaviUtils.so'
        pickFirst 'lib/*/libGNaviMapex.so'
        pickFirst 'lib/*/libGNaviData.so'
        pickFirst 'lib/*/libGNaviMap.so'
        pickFirst 'lib/*/libGNaviSearch.so'
        pickFirst 'lib/*/libDJIFlySafeCore.so'
        pickFirst 'lib/*/libdjifs_jni.so'
        pickFirst 'lib/*/libsfjni.so'
        exclude 'META-INF/proguard/okhttp3.pro'
        exclude 'META-INF/rxjava.properties'
        exclude 'assets/location_map_gps_locked.png'
        exclude 'assets/location_map_gps_3d.png'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

// 依赖解析策略 - 强制使用统一的高德地图SDK版本
configurations.all {
    resolutionStrategy {
        force 'com.amap.api:3dmap:9.2.1'
        force 'com.amap.api:search:9.7.1'
        force 'com.amap.api:location:6.4.9'

        // 解决版本冲突时优先使用指定版本
        eachDependency { details ->
            if (details.requested.group == 'com.amap.api') {
                switch (details.requested.name) {
                    case '3dmap':
                        details.useVersion '9.2.1'
                        break
                    case 'search':
                        details.useVersion '9.7.1'
                        break
                    case 'location':
                        details.useVersion '6.4.9'
                        break
                }
            }
        }
    }
}

dependencies {
    implementation ('com.dji:dji-uxsdk:4.16.2', {
        /**
         * Exclude amap from DJI SDK to avoid version conflicts
         * We will use our own unified amap dependencies*/

         exclude group: 'com.amap.api'
    })
    implementation project(path: ':uxe-sdk')
    implementation('com.dji:dji-sdk:4.18', {
        exclude module: 'library-anti-distortion'
    })
    compileOnly 'com.dji:dji-sdk-provided:4.18'

    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.cardview:cardview:1.0.0'

    // retrofit
    api 'com.squareup.retrofit2:retrofit:2.4.0'
    api 'com.squareup.okhttp3:logging-interceptor:3.11.0'

    // 高德地图 - 统一版本管理，避免依赖冲突
    // 使用兼容的版本组合
    implementation 'com.amap.api:3dmap-location-search:latest.integration'
    /*implementation 'com.amap.api:3dmap:10.0.600'
    implementation 'com.amap.api:search:9.7.4'  // 使用最新稳定版本
    implementation 'com.amap.api:location:6.4.9' // 使用稳定版本*/

    implementation 'org.greenrobot:eventbus:3.0.0'

    implementation 'com.alibaba:fastjson:1.1.67.android'

    api("com.otaliastudios:cameraview:2.7.2")

    implementation 'com.github.lzyzsd:jsbridge:1.0.4'

    implementation 'com.google.android.material:material:1.1.0'

    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.0'
    implementation 'com.elvishew:xlog:1.6.1'
    implementation 'commons-net:commons-net:3.6'

    implementation 'com.tencent.bugly:crashreport:latest.release'

    implementation 'org.greenrobot:eventbus:3.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation 'androidx.annotation:annotation:1.2.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.core:core:1.3.2'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
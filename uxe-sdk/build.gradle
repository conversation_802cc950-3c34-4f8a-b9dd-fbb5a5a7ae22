apply plugin: 'com.android.library'

repositories {
    mavenLocal()
    jcenter()
}

android {
    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 1
        versionName "5.0"
        vectorDrawables.useSupportLibrary = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
   /* buildToolsVersion = '28.0.3'*/
}

dependencies {
    // DJI SDK
    /*compileOnly ('com.dji:dji-uxsdk:4.14', {
        exclude module: 'dji-sdk'
        exclude module: 'library-anti-distortion'
        exclude module: 'fly-safe-database'
        exclude group: 'com.amap.api'
    })

    compileOnly ('com.dji:dji-sdk:4.15', {
        exclude module: 'library-anti-distortion'
        exclude module: 'fly-safe-database'
        exclude group: 'com.amap.api'
    })

    compileOnly ('com.dji:dji-sdk-provided:4.15')*/
    implementation ('com.dji:dji-uxsdk:4.16.2', {
        /**
         * Exclude amap from DJI SDK to avoid version conflicts
         * App module will provide unified amap dependencies
         */
        exclude group: 'com.amap.api'
    })
    compileOnly 'com.dji:dji-sdk-provided:4.18'


    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'androidx.annotation:annotation:1.0.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
}
